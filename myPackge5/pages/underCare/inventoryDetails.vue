<template>
    <view>
        <CustomNavbar :title="'详情'" :titleColor="'##333333'" />
        <view class="header">
            <view class="fifter static-title">养殖场详情</view>
            <view class="fifter">
                <img src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/xiaoshouhetong/shaixuan.png"
                    alt="" @click="fifterClick" />
            </view>
        </view>

        <view class="main-content" >
            <CommonTable
                :columns="tableColumns"
                :data="tableData"
                @row-click="handleRowClick"
            />
        </view>
        <CommonPagination
                :current="pagination.current"
                :page-size="pagination.pageSize"
                :total="pagination.total"
                @change="handlePageChange"
            />
        <filterPopup @resetSearch="resetSearch" :filterType="filterType" :pickerFilterShow="pickerFilterShow"
            @canel="pickerFilterShow = false" @submitForm="submitForm" />

    </view>
</template>

<script>
import CustomNavbar from '../components/CustomNavbar.vue'
import filterPopup from './components/filterPopup.vue'
import CommonTable from '../components/CommonTable.vue'
import CommonPagination from '../components/CommonPagination.vue'


export default {
    name: 'inventoryDetails',
    components: {
        CustomNavbar,
        filterPopup,
        CommonTable,
        CommonPagination
    },

    data() {
        return {
            systemInfo: uni.getSystemInfoSync(),
            filterType: 'inventory',
            pickerFilterShow: false,
            // 表格列配置
            tableColumns: [
                { title: '耳标号', key: 'earTagNumber', width: '120rpx', align: 'center' },
                { title: '品种', key: 'breed', width: '120rpx', align: 'center' },
                { title: '栏位', key: 'pen', width: '100rpx', align: 'center' },
                { title: '体重（kg）', key: 'weight', width: '140rpx', align: 'center' },
                { title: '入库批次', key: 'batchNumber', width: '140rpx', align: 'center' },
                { title: '健康状况', key: 'healthStatus', width: '120rpx', align: 'center' }
            ],
            // 表格数据
            tableData: [],
            // 分页配置
            pagination: {
                current: 1,
                pageSize: 10,
                total: 300 // 设置初始总数，避免分页组件计算错误
            },
            // 筛选参数
            filterParams: {},
            // 加载状态
            loading: false
        }
    },

    computed: {
        navbarTotalHeight() {
            return (this.systemInfo.statusBarHeight || 0) + 44;
        }
    },

    onLoad() {
        // 页面加载时立即加载数据
        this.loadTableData();
    },

    onReady() {
        // 页面准备完成后再次确保数据加载
        if (this.tableData.length === 0) {
            this.loadTableData();
        }
    },

    methods: {
        resetSearch() {
            console.log('resetSearch');
            this.filterParams = {};
            this.pagination.current = 1;
            this.loadTableData();
        },
        submitForm(val) {
            this.filterParams = { ...val };
            this.pickerFilterShow = false;
            this.pagination.current = 1;
            this.loadTableData();
        },
        fifterClick(){
            this.pickerFilterShow = true;
        },
        // 加载表格数据
        async loadTableData() {
            console.log('开始加载数据，当前页:', this.pagination.current);
            this.loading = true;
            try {
                const mockData = this.generateMockData();
                console.log('生成的模拟数据:', mockData);

                // const response = await this.fetchInventoryData({
                //     page: this.pagination.current,
                //     pageSize: this.pagination.pageSize,
                //     ...this.filterParams
                // });

                this.tableData = mockData.list;
                this.pagination.total = mockData.total;
                console.log('数据加载完成，表格数据条数:', this.tableData.length, '总条数:', this.pagination.total);
            } catch (error) {
                console.error('加载数据失败:', error);
                uni.showToast({
                    title: '加载数据失败',
                    icon: 'none'
                });
            } finally {
                this.loading = false;
            }
        },
        // 生成模拟数据
        generateMockData() {
            const total = 300;
            const pageSize = this.pagination.pageSize;
            const current = this.pagination.current;
            const startIndex = (current - 1) * pageSize;

            const list = [];
            for (let i = 0; i < pageSize && startIndex + i < total; i++) {
                const index = startIndex + i + 1;
                list.push({
                    earTagNumber: `ND${String(index).padStart(3, '0')}`,
                    breed: '安格斯牛',
                    pen: `A区1栏`,
                    weight: 512,
                    batchNumber: '2024-01',
                    healthStatus: '良好'
                });
            }

            return {
                list,
                total
            };
        },
        async fetchInventoryData(params) {
            console.log('API参数:', params);
            // return await uni.request({
            //     url: '/api/inventory/list',
            //     method: 'GET',
            //     data: params
            // });
        },
        // 分页
        handlePageChange(page) {
            console.log('分页切换到第', page, '页');
            this.pagination.current = page;
            this.loadTableData();
        },
        // 表格行点击
        handleRowClick(row, index) {
            console.log('点击行:', row, index);
        }
    }
}
</script>

<style lang="scss" scoped>
.header {
    width: 750rpx;
    height: 250rpx;
    display: flex;
    padding-top: 120rpx;
    box-sizing: border-box;
    position: relative;
    position: relative;
    .static-title {
        left: 30rpx;
        font-size: 32rpx;
        color: #333333;
        font-weight: bold;
    }
}

.fifter {
    position: absolute;
    top: 197rpx;
    right: 30rpx;

    img {
        width: 34rpx;
        height: 32.5rpx;
    }
}

.main-content {
    background: #fff;
    height: 980rpx;
    margin: 10rpx 30rpx 50rpx 30rpx;
    border-radius: 30rpx;
    padding: 0 20rpx 40rpx 20rpx;
    overflow: auto;
    /deep/ .common-table{
        margin-top: 20rpx;
    }

    :deep(.common-pagination) {
        background: transparent;
        margin-top: 20rpx;
    }
}
</style>
